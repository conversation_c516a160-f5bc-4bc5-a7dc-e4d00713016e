import React from 'react';
import { render, screen } from '@testing-library/react';
import VesselGrid from '../VesselGrid';
import { Vessel } from '../../../types/types';

// Mock the hooks and components
jest.mock('../../../hooks/useInfiniteScroll', () => ({
  useInfiniteScroll: () => ({
    containerRef: { current: null },
    handleScroll: jest.fn(),
  }),
}));

jest.mock('../Spinner', () => {
  return function MockSpinner() {
    return <div data-testid="spinner">Loading...</div>;
  };
});

describe('VesselGrid', () => {
  const mockVessels: Vessel[] = [
    {
      name: 'Vessel Alpha',
      vesselData: [{ status: 'active' }],
      type: 'cargo',
      vessel_ownership_id: 101,
      risk_id: 1,
      vessel_id: 1,
    },
    {
      name: 'Vessel Beta',
      vesselData: [{ status: 'inactive' }],
      type: 'tanker',
      vessel_ownership_id: 102,
      risk_id: 2,
      vessel_id: 2,
    },
  ];

  const defaultProps = {
    vessels: mockVessels,
    tableHeaders: ['Name', 'Status', 'Type'],
    badgeColors: ['#ff0000', '#00ff00', '#0000ff'],
    isFetchingNextPage: false,
    isLoading: false,
    fetchNextPage: jest.fn(),
    pagination: {
      totalItems: 10,
      totalPages: 2,
      page: 1,
      pageSize: 5,
    },
    isModal: false,
    gridComponent: 'bar' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading state when isLoading is true', () => {
    render(<VesselGrid {...defaultProps} isLoading={true} />);
    
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should render no results message when vessels array is empty', () => {
    render(<VesselGrid {...defaultProps} vessels={[]} isLoading={false} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should render bar chart wrapper for default gridComponent', () => {
    render(<VesselGrid {...defaultProps} />);
    
    const wrapper = screen.getByText('No results found').parentElement;
    expect(wrapper).toHaveClass('vessel-bar-chart-wrapper');
  });

  it('should render bar chart wrapper for bar gridComponent', () => {
    render(<VesselGrid {...defaultProps} gridComponent="bar" />);
    
    const wrapper = screen.getByText('No results found').parentElement;
    expect(wrapper).toHaveClass('vessel-bar-chart-wrapper');
  });

  it('should render empty content for pie gridComponent', () => {
    render(<VesselGrid {...defaultProps} gridComponent="pie" />);
    
    // For pie chart, the component returns empty content in the current implementation
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should render empty content for dashboard gridComponent', () => {
    render(<VesselGrid {...defaultProps} gridComponent="dashboard" />);
    
    // For dashboard, the component returns empty content in the current implementation
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should show loading indicator when fetching next page', () => {
    render(<VesselGrid {...defaultProps} isFetchingNextPage={true} />);
    
    const loadingIndicators = screen.getAllByTestId('spinner');
    expect(loadingIndicators).toHaveLength(1); // One for the loading indicator
  });

  it('should not show loading indicator when not fetching next page', () => {
    render(<VesselGrid {...defaultProps} isFetchingNextPage={false} />);
    
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
  });

  it('should handle pagination correctly', () => {
    const mockUseInfiniteScroll = require('../../../hooks/useInfiniteScroll').useInfiniteScroll;
    const mockFetchNextPage = jest.fn();

    mockUseInfiniteScroll.mockReturnValue({
      containerRef: { current: null },
      handleScroll: jest.fn(),
    });

    render(
      <VesselGrid
        {...defaultProps}
        fetchNextPage={mockFetchNextPage}
        pagination={{
          totalItems: 20,
          totalPages: 4,
          page: 2,
          pageSize: 5,
        }}
      />
    );

    // useInfiniteScroll should be called with correct parameters
    expect(mockUseInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: mockFetchNextPage,
      isFetchingNextPage: false,
      hasNextPage: true, // page 2 < totalPages 4
      dataLength: 2, // vessels.length
    });
  });

  it('should handle no pagination', () => {
    const mockUseInfiniteScroll = require('../../../hooks/useInfiniteScroll').useInfiniteScroll;

    render(<VesselGrid {...defaultProps} pagination={undefined} />);

    expect(mockUseInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: expect.any(Function),
      isFetchingNextPage: false,
      hasNextPage: false, // no pagination means no next page
      dataLength: 2,
    });
  });

  it('should handle last page correctly', () => {
    const mockUseInfiniteScroll = require('../../../hooks/useInfiniteScroll').useInfiniteScroll;
    
    render(
      <VesselGrid 
        {...defaultProps} 
        pagination={{
          totalItems: 10,
          totalPages: 2,
          page: 2, // Last page
          pageSize: 5,
        }}
      />
    );

    expect(mockUseInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: expect.any(Function),
      isFetchingNextPage: false,
      hasNextPage: false, // page 2 === totalPages 2
      dataLength: 2,
    });
  });

  it('should render with correct root class', () => {
    const { container } = render(<VesselGrid {...defaultProps} />);
    
    const rootElement = container.querySelector('.vessel-grid-root');
    expect(rootElement).toBeInTheDocument();
  });

  it('should handle scroll events', () => {
    const mockHandleScroll = jest.fn();
    const mockUseInfiniteScroll = require('../../../hooks/useInfiniteScroll').useInfiniteScroll;

    mockUseInfiniteScroll.mockReturnValue({
      containerRef: { current: null },
      handleScroll: mockHandleScroll,
    });

    const { container } = render(<VesselGrid {...defaultProps} />);

    const rootElement = container.querySelector('.vessel-grid-root');
    expect(rootElement).toHaveAttribute('onScroll');
  });

  it('should handle undefined vessels array', () => {
    render(<VesselGrid {...defaultProps} vessels={undefined as any} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should handle null vessels array', () => {
    render(<VesselGrid {...defaultProps} vessels={null as any} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should handle empty tableHeaders', () => {
    render(<VesselGrid {...defaultProps} tableHeaders={[]} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should handle empty badgeColors', () => {
    render(<VesselGrid {...defaultProps} badgeColors={[]} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should handle isModal prop', () => {
    render(<VesselGrid {...defaultProps} isModal={true} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should handle rest props spreading', () => {
    const extraProps = {
      'data-testid': 'vessel-grid-test',
      className: 'extra-class',
    };
    
    render(<VesselGrid {...defaultProps} {...extraProps} />);
    
    // The component should handle extra props gracefully
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should show loading state and loading indicator simultaneously', () => {
    render(
      <VesselGrid 
        {...defaultProps} 
        isLoading={true} 
        isFetchingNextPage={true} 
      />
    );
    
    const spinners = screen.getAllByTestId('spinner');
    expect(spinners).toHaveLength(1); // Only main loading spinner when isLoading is true
  });

  it('should handle vessels with missing properties', () => {
    const incompleteVessels: Vessel[] = [
      {
        name: 'Incomplete Vessel',
        vesselData: [],
        type: 'unknown',
      } as Vessel,
    ];
    
    render(<VesselGrid {...defaultProps} vessels={incompleteVessels} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should handle different gridComponent values', () => {
    const gridComponents = ['bar', 'pie', 'dashboard'] as const;
    
    gridComponents.forEach((component) => {
      const { unmount } = render(
        <VesselGrid {...defaultProps} gridComponent={component} />
      );
      
      expect(screen.getByText('No results found')).toBeInTheDocument();
      unmount();
    });
  });

  it('should handle complex pagination scenarios', () => {
    const scenarios = [
      { page: 1, totalPages: 1, expectedHasNext: false },
      { page: 1, totalPages: 5, expectedHasNext: true },
      { page: 3, totalPages: 3, expectedHasNext: false },
      { page: 2, totalPages: 10, expectedHasNext: true },
    ];

    scenarios.forEach(({ page, totalPages, expectedHasNext }) => {
      const mockUseInfiniteScroll = require('../../../hooks/useInfiniteScroll').useInfiniteScroll;

      const { unmount } = render(
        <VesselGrid
          {...defaultProps}
          pagination={{
            totalItems: totalPages * 5,
            totalPages,
            page,
            pageSize: 5,
          }}
        />
      );

      expect(mockUseInfiniteScroll).toHaveBeenCalledWith({
        fetchNextPage: expect.any(Function),
        isFetchingNextPage: false,
        hasNextPage: expectedHasNext,
        dataLength: 2,
      });

      unmount();
    });
  });
});
