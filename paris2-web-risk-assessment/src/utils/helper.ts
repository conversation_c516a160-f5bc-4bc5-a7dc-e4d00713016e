import {RaLevel, TemplateFormStatus} from '../enums';
import {
  Parameter,
  TemplateForm,
  TemplateFormCategory,
  TemplateFormHazard,
  TemplateFormJobResidualRiskRating,
} from '../types/template';
import {v4 as uuidv4} from 'uuid';
import * as _ from 'lodash';
import {riskMatrix} from '../constants/riskMatrix';
import {
  RiskForm,
  RiskCategory,
  RiskHazard,
  RiskFormJob,
  RiskTaskReliabilityAssessment,
  RiskJobResidualRiskRating,
  RiskJobInitialRiskRating,
  RAItemFull,
} from '../types/risk';
import {raStatusToLabel} from './common';
import {RiskParameter} from '../types';

/**
 * Input type for risk parameter transformation.
 * Represents the structure of risk parameter data from the API.
 */
type RiskParameterTypeInput = {
  id: number;
  name: string;
  parameters: {id: number; name: string}[];
};

/**
 * Output type for grouped options used in UI components.
 * Represents the transformed structure for checkbox grids and similar components.
 */
type GroupedOption = {
  id: number;
  label: string;
  options: {id: number; label: string}[];
  columns: number;
};

/**
 * Transforms risk parameter types into grouped options for UI rendering.
 * Converts API response format into a structure suitable for grouped checkbox components.
 *
 * @param {RiskParameterTypeInput[]} riskParameterType - Array of risk parameter types from API
 * @param {number} [columns=3] - Number of columns for the UI grid layout
 * @returns {GroupedOption[]} Array of grouped options for UI components
 *
 * @example
 * const apiData = [
 *   { id: 1, name: 'Safety', parameters: [{ id: 1, name: 'PPE' }, { id: 2, name: 'Training' }] }
 * ];
 * const options = generateGroupedOptions(apiData, 2);
 * // Returns: [{ id: 1, label: 'SAFETY', options: [{ id: 1, label: 'PPE' }, ...], columns: 2 }]
 */
export function generateGroupedOptions(
  riskParameterType: RiskParameterTypeInput[],
  columns = 3,
): GroupedOption[] {
  return riskParameterType.map(group => ({
    id: group.id,
    label: group.name.toUpperCase(),
    options: group.parameters.map(opt => ({
      id: opt.id,
      label: opt.name,
    })),
    columns,
  }));
}

/**
 * Utility to remove and reindex job state objects after a job is deleted.
 * Maintains sequential indexing by removing the specified index and reordering remaining items.
 *
 * @template T - Type extending Record<number, any>
 * @param {T} state - The current state object with numeric keys
 * @param {number} idx - The index to remove from the state
 * @returns {T} New state object with the specified index removed and remaining items reindexed
 *
 * @example
 * const currentState = { 0: { name: 'Job A' }, 1: { name: 'Job B' }, 2: { name: 'Job C' } };
 * const newState = removeAndReindexJobState(currentState, 1);
 * // Returns: { 0: { name: 'Job A' }, 1: { name: 'Job C' } }
 */
export function removeAndReindexJobState<T extends Record<number, any>>(
  state: T,
  idx: number,
): T {
  const newState: T = {} as T;
  Object.keys(state)
    .map(Number)
    .filter(i => i !== idx)
    .sort((a, b) => a - b)
    .forEach(i => {
      newState[i < idx ? i : i - 1] = state[i];
    });
  return newState;
}

/**
 * Comprehensive form parameter handler that cleans and validates payload data.
 * Removes empty fields, processes nested objects, and ensures data integrity
 * before sending to the API. Handles both template and risk assessment forms.
 *
 * @param {any} payload - The form payload to process and clean
 * @returns {any} Cleaned and processed payload ready for API submission
 *
 * @description
 * This function performs the following operations:
 * - Removes empty category, hazard, and job arrays
 * - Cleans up parameter arrays by filtering valid entries
 * - Processes risk ratings and reliability assessments
 * - Removes metadata fields like id, timestamps, and user info
 * - Handles special cases for draft submissions
 *
 * @example
 * const rawPayload = {
 *   template_category: { category_id: [] },
 *   worst_case_scenario: '',
 *   parameters: [{ parameter_id: [1, 2] }, { parameter_id: [] }]
 * };
 * const cleaned = formParameterHandler(rawPayload);
 * // Returns payload with empty fields removed and parameters filtered
 */
function cleanCategory(obj: any, key: string) {
  if (!obj?.[key]?.category_id?.length) delete obj[key];
  if (Array.isArray(obj?.[key])) {
    if (!obj[key][0]?.isOther && obj[key][0]?.value === '') {
      delete obj[key][0].value;
    }
    // Also check for value property on the array object itself
    if (!obj[key][0]?.isOther && (obj[key] as unknown as any)?.value === '') {
      delete (obj[key] as unknown as any).value;
    }
  } else if (obj?.[key]?.value === '' && !obj?.[key]?.is_other) {
    delete obj[key].value;
  }
}
function cleanHazard(obj: any, key: string) {
  if (!obj?.[key]?.hazard_id?.length && !obj?.[key]?.is_other) delete obj[key];
  if (Array.isArray(obj?.[key])) {
    if (!obj[key][0]?.isOther && obj[key][0]?.value === '') {
      delete obj[key][0].value;
    }
  } else if (obj?.[key]?.value === '' && !obj?.[key]?.isOther) {
    delete obj[key].value;
  }
}
function cleanJob(obj: any, key: string) {
  if (!obj?.[key]?.[0]?.job_step?.length) delete obj[key];
}
function cleanEmpty(obj: any, key: string) {
  if (!obj?.[key]?.length) delete obj[key];
}
function cleanParameters(params: any[]) {
  return params
    ?.filter(
      (param: any) =>
        (param?.parameter_id && param.parameter_id.length) || param?.is_other,
    )
    ?.map((param: any) => {
      if (param?.parameter_id?.length)
        param.parameter_id = _.uniq(param.parameter_id);
      if (param && param.is_other === false) {
        const {value, ...rest} = param;
        return rest;
      }
      return param;
    });
}
function assignIfNonEmpty(target: any, key: string, value: any) {
  if (value?.length > 0) {
    target[key] = value;
  }
}
function cleanJobs(jobs: any[]) {
  const cleaned = jobs
    ?.map(
      ({
        risk_job_residual_risk_rating,
        risk_job_initial_risk_rating,
        job_close_out_date,
        job_close_out_responsibility_id,
        job_close_out_responsibility_label,
        ...rest
      }: any) => {
        const processedJob: any = {...rest};
        if (Array.isArray(risk_job_residual_risk_rating)) {
          processedJob.risk_job_residual_risk_rating =
            risk_job_residual_risk_rating.map(
              ({rating, parameter_type_id, reason}: any) => {
                const base = {rating, parameter_type_id};
                return reason ? {...base, reason} : base;
              },
            );
        }
        if (Array.isArray(risk_job_initial_risk_rating)) {
          processedJob.risk_job_initial_risk_rating =
            risk_job_initial_risk_rating.map(
              ({rating, parameter_type_id}: any) => ({
                rating,
                parameter_type_id,
              }),
            );
        }

        assignIfNonEmpty(
          processedJob,
          'job_close_out_date',
          job_close_out_date,
        );
        assignIfNonEmpty(
          processedJob,
          'job_close_out_responsibility_id',
          job_close_out_responsibility_id,
        );
        assignIfNonEmpty(
          processedJob,
          'job_close_out_responsibility_label',
          job_close_out_responsibility_label,
        );

        return processedJob;
      },
    )
    ?.filter((job: any) => job.job_step && job.job_step.length > 0);
  return cleaned?.length ? cleaned : undefined;
}
function cleanTemplateReliability(arr: any[]) {
  return arr?.map((item: any) => {
    const {condition} = item;
    const newItem = {
      task_reliability_assessment_answer:
        item?.task_reliability_assessment_answer ||
        item?.task_reliability_assessmen ||
        '',
      task_reliability_assessment_id:
        item?.task_reliability_assessment_id || item?.id || null,
    };
    return !condition?.length ? newItem : {...newItem, condition};
  });
}
function cleanRiskReliability(arr: any[]) {
  return arr?.map((item: any) => {
    const {condition} = item;
    const newItem = {
      task_reliability_assessment_answer: condition?.length
        ? 'Yes'
        : item?.task_reliability_assessment_answer ||
          item?.task_reliability_assessmen ||
          '',
      task_reliability_assessment_id:
        item?.task_reliability_assessment_id || item?.id || null,
    };
    return !condition?.length ? newItem : {...newItem, condition};
  });
}
function cleanTeamMembers(arr: any[]) {
  return arr?.map((member: any) => {
    const cleaned: any = {};
    Object.entries(member).forEach(([key, value]) => {
      if (
        value !== null &&
        key !== 'status' &&
        key !== 'id' &&
        key !== 'risk_id' &&
        value !== ''
      )
        cleaned[key] = value;
    });
    return cleaned;
  });
}
function cleanMetadataFields(payload: any) {
  const fieldsToDelete = [
    'updated_at',
    'created_by',
    'updated_by',
    'vessel_id',
    'risk_approver',
    'id',
  ];
  fieldsToDelete.forEach(field => {
    if (field in payload) delete payload[field];
  });
}

function cleanPayloadCategories(payload: any) {
  cleanCategory(payload, 'template_category');
  cleanCategory(payload, 'risk_category');
  cleanHazard(payload, 'template_hazard');
  cleanHazard(payload, 'risk_hazard');
  cleanJob(payload, 'template_job');
}

function cleanPayloadEmptyFields(payload: any) {
  [
    'worst_case_scenario',
    'recovery_measures',
    'approval_required',
    'task_duration',
  ].forEach(field => {
    cleanEmpty(payload, field);
  });
}

function cleanPayloadMandatoryFields(payload: any) {
  ['assessor', 'date_risk_assessment', 'vessel_ownership_id'].forEach(field => {
    if (!payload?.[field]) delete payload[field];
  });
  if (!payload?.office_id) {
    delete payload.office_id;
    delete payload.office_name;
  }
}

function cleanPayloadParameters(payload: any) {
  payload.parameters = cleanParameters(payload?.parameters);
}

function cleanPayloadJobs(payload: any) {
  if (Array.isArray(payload.template_job)) {
    payload.template_job = payload.template_job.map(
      ({
        id,
        job_id,
        template_job_residual_risk_rating,
        template_job_initial_risk_rating,
        job_close_out_date,
        job_close_out_responsibility_id,
        job_close_out_responsibility_label,
        ...rest
      }: any) => ({
        ...rest,
        ...(job_close_out_date ? {job_close_out_date} : {}),
        ...(job_close_out_responsibility_id
          ? {job_close_out_responsibility_id}
          : {}),
        ...(job_close_out_responsibility_label
          ? {job_close_out_responsibility_label}
          : {}),
        ...(Array.isArray(template_job_residual_risk_rating) && {
          template_job_residual_risk_rating:
            template_job_residual_risk_rating.map(
              ({rating, parameter_type_id, reason}: any) => ({
                rating,
                parameter_type_id,
                ...(reason && {reason}),
              }),
            ),
        }),
        ...(Array.isArray(template_job_initial_risk_rating) && {
          template_job_initial_risk_rating:
            template_job_initial_risk_rating.map(
              ({rating, parameter_type_id}: any) => ({
                rating,
                parameter_type_id,
              }),
            ),
        }),
      }),
    );
  }

  if (Array.isArray(payload.risk_job)) {
    const jobs = cleanJobs(payload.risk_job);
    if (jobs) payload.risk_job = jobs;
    else delete payload.risk_job;
  }
}

function cleanPayloadReliability(payload: any) {
  if (payload.template_task_reliability_assessment?.length) {
    payload.template_task_reliability_assessment = cleanTemplateReliability(
      payload.template_task_reliability_assessment,
    );
  }
  if (payload.risk_task_reliability_assessment?.length) {
    payload.risk_task_reliability_assessment = cleanRiskReliability(
      payload.risk_task_reliability_assessment,
    );
  }
}

function cleanPayloadTeamMembers(payload: any) {
  if (payload?.risk_team_member?.length) {
    payload.risk_team_member = cleanTeamMembers(payload.risk_team_member);
  }
}

export const formParameterHandler = (payload: any) => {
  cleanPayloadCategories(payload);
  cleanPayloadEmptyFields(payload);
  cleanPayloadMandatoryFields(payload);
  cleanMetadataFields(payload);
  cleanPayloadParameters(payload);
  cleanPayloadJobs(payload);
  cleanPayloadReliability(payload);
  cleanPayloadTeamMembers(payload);
  return payload;
};

/**
 * Maps incoming parameter data to the required Parameter[] format.
 * Groups parameters by type and handles 'other' parameter values.
 *
 * @param {any[]} input - Array of parameter data from the API
 * @returns {Parameter[]} Array of formatted Parameter objects
 *
 * @description
 * Groups parameters by parameterType.id and creates consolidated Parameter objects
 * with proper handling of custom 'other' parameter values.
 */
function mapParameters(input: any[]): Parameter[] {
  if (!Array.isArray(input)) return [];
  return _.chain(input)
    .groupBy(item => item?.parameterType?.id) // TBC parameter_type_id
    .map(items => {
      const hasOtherValue =
        items.filter(item => item.parameter_is_other)?.[0]?.value ?? '';
      return {
        is_other: !!hasOtherValue,
        parameter_type_id: items[0]?.parameterType?.id, // TBC parameter_type_id
        parameter_id: _.compact(_.map(items, 'parameter.id')) ?? [],
        value: hasOtherValue,
      };
    })
    .value();
}
/**
 * Maps hazard data from API format to TemplateFormHazard structure.
 * Separates predefined hazards from custom 'other' hazard values.
 *
 * @param {any[]} input - Array of hazard data from the API
 * @returns {TemplateFormHazard} Formatted hazard object with IDs and custom values
 *
 * @description
 * Processes hazard data by:
 * - Extracting custom hazard values from 'other' entries
 * - Collecting predefined hazard IDs from hazard_detail entries
 * - Setting appropriate flags for custom vs predefined hazards
 */
function mapHazards(input: any[]): TemplateFormHazard {
  const hasOtherValue =
    input?.filter(item => item.hazard_category_is_other)?.[0]?.value ?? '';
  return {
    is_other: !!hasOtherValue,
    value: hasOtherValue,
    hazard_id:
      _.compact(
        input
          ?.filter(
            item => !item?.hazard_category_is_other && item?.hazard_detail,
          )
          .map(item => item?.hazard_detail?.id),
      ) ?? [],
  };
}

/**
 * Maps category data from API format to TemplateFormCategory structure.
 * Handles both predefined categories and custom 'other' category values.
 *
 * @param {any[]} [input] - Array of category data from the API
 * @returns {TemplateFormCategory} Formatted category object with IDs and custom values
 *
 * @description
 * Processes category data by:
 * - Extracting custom category values from 'other' entries
 * - Collecting predefined category IDs from category entries
 * - Providing default empty structure if no input provided
 */
function mapCategories(input?: any[]): TemplateFormCategory {
  if (!Array.isArray(input)) {
    return {
      is_other: false,
      value: '',
      category_id: [],
    };
  }

  const otherItem = input.find(item => item.category_is_other);
  const hasOtherValue = otherItem?.value ?? '';

  return {
    is_other: !!hasOtherValue,
    value: hasOtherValue,
    category_id: input
      .filter(item => !item.category_is_other)
      .map(item => item.category?.id)
      .filter(Boolean),
  };
}

/**
 * Creates a TemplateForm object from API data or initializes with defaults.
 * Transforms raw API response into the structured format required by the form.
 *
 * @param {any} [data] - Optional API data to transform into TemplateForm
 * @returns {TemplateForm} Complete TemplateForm object with all required fields
 *
 * @description
 * Converts API response data into a properly structured TemplateForm object:
 * - Maps basic fields with fallback to empty strings
 * - Processes complex nested objects (categories, hazards, parameters)
 * - Generates unique job IDs for template jobs
 * - Provides default job structure if none exists
 * - Preserves metadata fields (created_by, updated_by, etc.)
 *
 * @example
 * const apiData = { task_requiring_ra: 'Engine Maintenance', template_job: [...] };
 * const form = createFormFromData(apiData);
 * // Returns complete TemplateForm with all required fields populated
 */
export const createFormFromData = (data?: any): TemplateForm => {
  return {
    task_requiring_ra: data?.task_requiring_ra ?? '',
    task_duration: data?.task_duration ?? '',
    task_alternative_consideration: data?.task_alternative_consideration ?? '',
    task_rejection_reason: data?.task_rejection_reason ?? '',
    worst_case_scenario: data?.worst_case_scenario ?? '',
    recovery_measures: data?.recovery_measures ?? '',
    status: TemplateFormStatus.DRAFT,

    parameters: data?.template_parameter?.length
      ? mapParameters(data?.template_parameter)
      : [],

    template_category: mapCategories(data?.template_category),
    template_job: data?.template_job?.length
      ? data?.template_job?.map((job: any) => ({
          ...job,
          job_id: uuidv4(),
        }))
      : [
          {
            job_id: uuidv4(),
            job_step: '',
            job_hazard: '',
            job_nature_of_risk: '',
            job_existing_control: '',
            job_additional_mitigation: '',
            job_close_out_date: '',
            job_close_out_responsibility_id: '',
            job_close_out_responsibility_label: '',
            template_job_initial_risk_rating: [],
            template_job_residual_risk_rating: [],
          },
        ],
    template_hazard: mapHazards(data?.template_hazards),
    template_task_reliability_assessment:
      data?.template_task_reliability_assessment ?? [],
    template_keyword: data?.template_keyword ?? ['test'],
    updated_at: data?.updated_at ?? undefined,
    updated_by: data?.updated_by ?? undefined,
    created_by: data?.created_by ?? undefined,
  };
};

/**
 * Validates if a required field is empty or contains only whitespace.
 *
 * @param {string} [value] - The field value to validate
 * @returns {boolean} True if field is empty/invalid, false if valid
 *
 * @example
 * validateRequiredField('') // Returns: true (invalid)
 * validateRequiredField('  ') // Returns: true (invalid)
 * validateRequiredField('Valid input') // Returns: false (valid)
 */
export const validateRequiredField = (value?: string): boolean => {
  return !value?.trim();
};

/**
 * Validates that all task reliability assessments have answers.
 *
 * @param {any[]} assessments - Array of task reliability assessment objects
 * @returns {boolean} True if invalid (missing answers), false if all valid
 *
 * @description
 * Checks that:
 * - Assessment array is not empty
 * - Each assessment has a task_reliability_assessment_answer
 *
 * @example
 * const assessments = [{ task_reliability_assessment_answer: 'Yes' }];
 * validateTaskReliabilityAnswers(assessments) // Returns: false (valid)
 */
export const validateTaskReliabilityAnswers = (assessments: any[]): boolean => {
  return (
    !assessments.length ||
    assessments.some((item: any) => !item.task_reliability_assessment_answer)
  );
};

/**
 * Extracts the appropriate assessment array from either TemplateForm or RiskForm.
 *
 * @param {TemplateForm | RiskForm} form - The form object to extract assessments from
 * @returns {any[]} Array of task reliability assessments
 *
 * @description
 * Handles both form types by checking for the appropriate assessment property:
 * - TemplateForm: uses template_task_reliability_assessment
 * - RiskForm: uses risk_task_reliability_assessment
 */
export const getAssessmentArray = (form: TemplateForm | RiskForm): any[] => {
  if ('template_task_reliability_assessment' in form) {
    return Array.isArray(form.template_task_reliability_assessment)
      ? form.template_task_reliability_assessment
      : [];
  } else {
    return Array.isArray(form.risk_task_reliability_assessment)
      ? form.risk_task_reliability_assessment
      : [];
  }
};

const getAssementForRiskRating = (form: TemplateForm | RiskForm) => {
  let assessments: any[] = [];
  if ('template_task_reliability_assessment' in form) {
    if (Array.isArray(form.template_task_reliability_assessment)) {
      assessments = form.template_task_reliability_assessment;
    }
  } else if ('risk_task_reliability_assessment' in form) {
    if (
      Array.isArray(
        (form as unknown as RiskForm).risk_task_reliability_assessment,
      )
    ) {
      assessments = (form as unknown as RiskForm)
        .risk_task_reliability_assessment;
    }
  }
  return assessments;
};
const getJobsForRiskRating = (form: TemplateForm | RiskForm) => {
  let jobs: any[] = [];
  if ('template_job' in form) {
    if (Array.isArray(form.template_job)) {
      jobs = form.template_job;
    }
  } else if ('risk_job' in form) {
    if (Array.isArray((form as unknown as RiskForm).risk_job)) {
      jobs = (form as unknown as RiskForm).risk_job;
    }
  }
  return jobs;
};

/**
 * Calculates the overall risk rating based on task reliability answers and job risk ratings.
 * Uses a combination of assessment answers and risk matrix calculations to determine the final rating.
 *
 * @param {TemplateForm | RiskForm} form - The form containing assessment and job data
 * @returns {string} Risk rating classification ('High', 'Medium', or 'Low')
 *
 * @description
 * Risk rating calculation logic:
 * 1. If any task reliability assessment answer is 'No', returns 'High'
 * 2. Calculates maximum rate from all job residual risk ratings
 * 3. Maps the maximum rate to risk classification using risk matrix
 * 4. Defaults to 'Medium' if no specific rating can be determined
 *
 * Supports both TemplateForm and RiskForm structures by detecting the appropriate
 * properties for assessments and jobs.
 *
 * @example
 * const form = {
 *   template_task_reliability_assessment: [{ task_reliability_assessment_answer: 'Yes' }],
 *   template_job: [{ template_job_residual_risk_rating: [{ rating: 'A1' }] }]
 * };
 * const rating = calculateRiskRating(form); // Returns: 'Low', 'Medium', or 'High'
 */
export const calculateRiskRating = (form: TemplateForm | RiskForm): string => {
  // Get assessments array
  const assessments: any[] = getAssementForRiskRating(form);

  // If any answer is 'No', return 'High'
  if (assessments.some(a => a.task_reliability_assessment_answer === 'No')) {
    return 'High';
  }

  // Get jobs array
  const jobs: any[] = getJobsForRiskRating(form);

  // Helper to extract all ratings from jobs
  const getRatings = (job: any): string[] => {
    if (
      'template_job_residual_risk_rating' in job &&
      Array.isArray(job.template_job_residual_risk_rating)
    ) {
      return job.template_job_residual_risk_rating
        .map((r: any) => r.rating)
        .filter(Boolean);
    }
    if (
      'risk_job_residual_risk_rating' in job &&
      Array.isArray(job.risk_job_residual_risk_rating)
    ) {
      return job.risk_job_residual_risk_rating
        .map((r: any) => r.rating)
        .filter(Boolean);
    }
    return [];
  };

  // Flatten all ratings from all jobs
  const allRatings = jobs.flatMap(getRatings);

  // Find max rate from riskMatrix
  const maxRate = allRatings.reduce((max, rating) => {
    const matrixItem = riskMatrix.find(item => item.value === rating);
    return matrixItem &&
      typeof matrixItem.rate === 'number' &&
      matrixItem.rate > max
      ? matrixItem.rate
      : max;
  }, 0);

  if (maxRate > 0) {
    const maxRateObj = riskMatrix.find(item => item.rate === maxRate);
    if (maxRateObj) {
      return (
        maxRateObj.class.charAt(0).toUpperCase() +
        maxRateObj.class.slice(1).toLowerCase()
      );
    }
  }

  return 'Medium';
};

/**
 * Returns the background color for risk rating display components.
 *
 * @param {string} rating - The risk rating ('High', 'Medium', or 'Low')
 * @returns {string} Hex color code for the background
 *
 * @example
 * getRiskRatingBackgroundColor('High') // Returns: '#FAF2F5'
 * getRiskRatingBackgroundColor('Medium') // Returns: '#FFF8E1'
 * getRiskRatingBackgroundColor('Low') // Returns: '#F2FAF2'
 */
export const getRiskRatingBackgroundColor = (rating: string): string => {
  switch (rating) {
    case 'High':
      return '#FAF2F5';
    case 'Medium':
      return '#FFF8E1';
    default:
      // Low
      return '#F2FAF2';
  }
};

/**
 * Returns the text color for risk rating display components.
 *
 * @param {string} rating - The risk rating ('High', 'Medium', or 'Low')
 * @returns {string} Hex color code for the text
 *
 * @example
 * getRiskRatingTextColor('High') // Returns: '#C82333'
 * getRiskRatingTextColor('Medium') // Returns: '#FFA500'
 * getRiskRatingTextColor('Low') // Returns: '#218838'
 */
export const getRiskRatingTextColor = (rating: string): string => {
  switch (rating) {
    case 'High':
      return '#C82333';
    case 'Medium':
      return '#FFA500';
    default:
      // Low
      return '#218838';
  }
};

/**
 * Maps risk category data from API format to RiskCategory structure.
 *
 * @param {any} [input] - Category data from the API
 * @returns {RiskCategory} Formatted risk category object
 *
 * @description
 * Processes category data for risk assessments, handling both predefined
 * categories and custom 'other' category values.
 */
function mapRiskCategory(input?: any[]): RiskCategory {
  const hasOtherValue =
    input?.filter(item => item.category_is_other)?.[0]?.value ?? '';
  return {
    is_other: !!hasOtherValue,
    category_id: _.compact(input?.map((item: any) => item?.category?.id)) ?? [],
    value: hasOtherValue,
  };
}

/**
 * Maps job data from API format to RiskFormJob array structure.
 * Creates default job structure if no input provided.
 *
 * @param {any[]} [input] - Array of job data from the API
 * @returns {RiskFormJob[]} Array of formatted risk form job objects
 *
 * @description
 * Processes job data for risk assessments:
 * - Maps all job fields with fallback to empty strings
 * - Preserves risk rating arrays (initial and residual)
 * - Provides default empty job structure if no input
 */
function mapRiskJobs(input?: any[]): RiskFormJob[] {
  if (!Array.isArray(input) || !input.length) {
    return [
      {
        job_step: '',
        job_hazard: '',
        job_nature_of_risk: '',
        job_additional_mitigation: '',
        job_close_out_date: '',
        job_existing_control: '',
        job_close_out_responsibility_id: '',
        job_close_out_responsibility_label: '',
        risk_job_initial_risk_rating: [],
        risk_job_residual_risk_rating: [],
      },
    ];
  }

  return input.map(job => ({
    job_step: job?.job_step ?? '',
    job_hazard: job?.job_hazard ?? '',
    job_nature_of_risk: job?.job_nature_of_risk ?? '',
    job_additional_mitigation: job?.job_additional_mitigation ?? '',
    job_close_out_date: job?.job_close_out_date ?? '',
    job_existing_control: job?.job_existing_control ?? '',
    job_close_out_responsibility_id: job?.job_close_out_responsibility_id ?? '',
    job_close_out_responsibility_label:
      job?.job_close_out_responsibility_label ?? '',
    risk_job_initial_risk_rating: job?.risk_job_initial_risk_rating ?? [],
    risk_job_residual_risk_rating: job?.risk_job_residual_risk_rating ?? [],
  }));
}

/**
 * Maps task reliability assessment data from API format to RiskTaskReliabilityAssessment array.
 *
 * @param {any[]} [input] - Array of task reliability assessment data from the API
 * @returns {RiskTaskReliabilityAssessment[]} Array of formatted assessment objects
 *
 * @description
 * Processes reliability assessment data:
 * - Maps assessment ID, answer, and condition fields
 * - Provides fallback values for missing data
 * - Returns empty array if no input provided
 */
function mapRiskTaskReliabilityAssessment(
  input?: any[],
): RiskTaskReliabilityAssessment[] {
  if (!Array.isArray(input)) return [];
  return input.map(assessment => ({
    task_reliability_assessment_id:
      assessment?.task_reliability_assessment_id ?? 0,
    task_reliability_assessment_answer:
      assessment?.task_reliability_assessment_answer ?? '',
    condition: assessment?.condition ?? '',
  }));
}

/**
 * Creates a RiskForm object from API data or initializes with defaults.
 * Transforms raw API response into the structured format required by the risk assessment form.
 *
 * @param {any} [data] - Optional API data to transform into RiskForm
 * @returns {RiskForm} Complete RiskForm object with all required fields
 *
 * @description
 * Converts API response data into a properly structured RiskForm object:
 * - Maps basic fields (task details, assessor, vessel info, dates)
 * - Processes complex nested objects (categories, hazards, parameters, jobs)
 * - Handles approval workflow data (required approvals, approvers, RA level)
 * - Converts status codes to labels using raStatusToLabel mapping
 * - Calculates final approval date based on approver data and RA level
 * - Preserves metadata fields (created_by, updated_by, etc.)
 *
 * @example
 * const apiData = {
 *   task_requiring_ra: 'Engine Maintenance',
 *   vessel_ownership_id: 123,
 *   risk_job: [{ job_step: 'Preparation', ... }],
 *   status: 1 // DRAFT
 * };
 * const form = createRiskFormFromData(apiData);
 * // Returns complete RiskForm with all required fields populated
 */
const safe = <T>(value: T | undefined, fallback: T): T => value ?? fallback;
const resolveStatus = (status?: number): string =>
  status ? raStatusToLabel[status] : 'DRAFT';

export const createRiskFormFromData = (data?: any): RiskForm => {
  const getApprovalRequired = (approvalRequired: any[]) =>
    Array.isArray(approvalRequired)
      ? approvalRequired.map((item: any) => item?.approval_required?.id)
      : [];

  const getRiskTeamMember = (teamMembers: any[]) =>
    Array.isArray(teamMembers)
      ? teamMembers.map((member: any) => ({
          ...member,
          ...(member.email && {email: atob(member.email)}),
        }))
      : [];

  return {
    template_id: data?.template_id ?? undefined,
    task_requiring_ra: safe(data?.task_requiring_ra, ''),
    assessor: data?.assessor ?? undefined,
    vessel_ownership_id: safe(data?.vessel_ownership_id, 0),
    office_id: safe(data?.office_id, 0),
    office_name: safe(data?.office_name, ''),
    vessel_id: data?.vessel_id ?? undefined,
    date_risk_assessment: data?.date_risk_assessment ?? undefined,
    task_duration: safe(data?.task_duration, ''),
    task_alternative_consideration: safe(
      data?.task_alternative_consideration,
      '',
    ),
    task_rejection_reason: safe(data?.task_rejection_reason, ''),
    worst_case_scenario: safe(data?.worst_case_scenario, ''),
    recovery_measures: safe(data?.recovery_measures, ''),
    status: resolveStatus(data?.status),
    approval_required: getApprovalRequired(data?.risk_approval_required),
    risk_team_member: getRiskTeamMember(data?.risk_team_member),
    risk_category: mapRiskCategory(data?.risk_category),
    risk_hazard: mapHazards(data?.risk_hazards),
    parameters: mapParameters(data?.risk_parameter),
    risk_job: mapRiskJobs(data?.risk_job),
    risk_task_reliability_assessment: mapRiskTaskReliabilityAssessment(
      data?.risk_task_reliability_assessment,
    ),
    updated_at: data?.updated_at ?? undefined,
    updated_by: data?.updated_by ?? undefined,
    created_by: data?.created_by ?? undefined,
    ra_level: data?.ra_level ?? undefined,
    approval_date:
      getFinalApprovalDate(data?.risk_approver, data?.ra_level) ?? undefined,
    risk_approver: data?.risk_approver ?? [],
  };
};

/**
 * Groups risk parameters by parameter type for organized display.
 * Transforms flat parameter array into grouped structure for UI components.
 *
 * @param {RiskParameter[]} data - Array of risk parameters to group
 * @returns {Array} Array of grouped parameter objects with type information
 *
 * @description
 * Groups parameters by their parameter_type.id and creates structured objects:
 * - Groups by parameter type ID using lodash groupBy
 * - Extracts type information (id, name) from the first item in each group
 * - Maps parameter details (id, name) for each parameter in the group
 * - Returns empty array if input is not a valid array
 *
 * @example
 * const params = [
 *   { id: 1, name: 'PPE', parameter_type: { id: 1, name: 'Safety' } },
 *   { id: 2, name: 'Training', parameter_type: { id: 1, name: 'Safety' } }
 * ];
 * const grouped = groupRiskParameters(params);
 * // Returns: [{ id: 1, name: 'Safety', parameters: [{ id: 1, name: 'PPE' }, ...] }]
 */
export const groupRiskParameters = (data: RiskParameter[]) => {
  if (!Array.isArray(data)) return [];

  return Object.values(_.groupBy(data, item => item?.parameter_type?.id)).map(
    items => ({
      id: items[0]?.parameter_type?.id,
      name: items[0]?.parameter_type?.name,
      parameters: items.map(i => ({
        id: i?.id,
        name: i?.name,
      })),
    }),
  );
};

/**
 * Converts a template API response to a RiskForm object for creating risk assessments from templates.
 * Transforms template data structure into the format required by risk assessment forms.
 *
 * @param {any} payload - Template API response data
 * @returns {RiskForm} RiskForm object populated with template data
 *
 * @description
 * Transforms template data into risk assessment format:
 * - Maps basic template fields to corresponding risk form fields
 * - Converts template-specific objects (template_category, template_hazard, etc.) to risk equivalents
 * - Processes template jobs and converts risk ratings appropriately
 * - Handles parameter grouping and mapping from template format
 * - Initializes risk-specific fields (team members, approvals, vessel info) with defaults
 * - Preserves template metadata and assigns template_id reference
 *
 * Key transformations:
 * - template_category → risk_category
 * - template_hazard → risk_hazard
 * - template_job → risk_job (with rating structure conversion)
 * - template_parameter → parameters (with proper grouping)
 * - template_task_reliability_assessment → risk_task_reliability_assessment
 *
 * @example
 * const templateData = {
 *   id: 123,
 *   task_requiring_ra: 'Engine Maintenance',
 *   template_job: [{ job_step: 'Preparation', template_job_residual_risk_rating: [...] }]
 * };
 * const riskForm = transformTemplateToRisk(templateData);
 * // Returns RiskForm with template_id: 123 and all template data mapped appropriately
 */
// Helper: Map risk category
const mapRiskCategoryFromTemplate = (templateCategory: any[]): RiskCategory => {
  const otherCategory = templateCategory?.find((c: any) => c.category_is_other);
  return {
    is_other: !!otherCategory,
    category_id: Array.isArray(templateCategory)
      ? templateCategory
          .filter((c: any) => !c.category_is_other && c.category)
          .map((c: any) => c.category?.id)
      : [],
    value: otherCategory?.value || '',
  };
};

// Helper: Map risk hazard
const mapRiskHazardFromTemplate = (templateHazards: any[]): RiskHazard => {
  const otherHazard = templateHazards?.find(
    (h: any) => h.hazard_category_is_other,
  );
  return {
    is_other: !!otherHazard,
    hazard_id: Array.isArray(templateHazards)
      ? templateHazards
          .filter((h: any) => !h.hazard_category_is_other && h.hazard_detail)
          .map((h: any) => h.hazard_detail?.id)
      : [],
    value: otherHazard?.value || '',
  };
};

// Helper: Map parameters
const mapParametersFromTemplate = (templateParameter: any[]): Parameter[] => {
  if (!Array.isArray(templateParameter)) return [];
  const grouped: {[typeId: number]: any[]} = {};
  for (const item of templateParameter) {
    const typeId = item?.parameterType?.id;
    if (!typeId) continue;
    if (!grouped[typeId]) grouped[typeId] = [];
    grouped[typeId].push(item);
  }
  return Object.values(grouped).map((items: any[]) => {
    const is_other = !!items.find(i => i.parameter_is_other);
    const value = items.find(i => i.parameter_is_other)?.value ?? '';
    return {
      is_other,
      parameter_type_id: items[0]?.parameterType?.id,
      parameter_id: _.uniq(
        items.filter(i => i?.parameter?.id).map(i => i.parameter.id),
      ),
      value: is_other ? value : '',
    };
  });
};

// Helper: Map jobs
const mapRiskJobsFromTemplate = (templateJobs: any[]): RiskFormJob[] => {
  if (!Array.isArray(templateJobs)) return [];
  return templateJobs.map((job: any) => ({
    job_step: safe(job.job_step, ''),
    job_hazard: safe(job.job_hazard, ''),
    job_nature_of_risk: safe(job.job_nature_of_risk, ''),
    job_additional_mitigation: safe(job.job_additional_mitigation, ''),
    job_close_out_date: safe(job.job_close_out_date, undefined),
    job_existing_control: safe(job.job_existing_control, ''),
    job_close_out_responsibility_id: safe(
      job.job_close_out_responsibility_id,
      '',
    ),
    job_close_out_responsibility_label: safe(
      job.job_close_out_responsibility_label,
      '',
    ),
    risk_job_initial_risk_rating: Array.isArray(
      job.template_job_initial_risk_rating,
    )
      ? job.template_job_initial_risk_rating.map(
          (r: any): RiskJobInitialRiskRating => ({
            parameter_type_id: r.parameter_type_id ?? 0,
            rating: r.rating,
          }),
        )
      : [],
    risk_job_residual_risk_rating: Array.isArray(
      job.template_job_residual_risk_rating,
    )
      ? job.template_job_residual_risk_rating.map(
          (
            r: TemplateFormJobResidualRiskRating,
          ): RiskJobResidualRiskRating => ({
            parameter_type_id: r.parameter_type_id ?? 0,
            rating: r.rating,
            reason: r.reason ?? '',
          }),
        )
      : [],
  }));
};

// Helper: Map reliability assessment
const mapReliabilityAssessmentFromTemplate = (
  templateAssessments: any[],
): RiskTaskReliabilityAssessment[] =>
  Array.isArray(templateAssessments)
    ? templateAssessments.map((item: any) => ({
        task_reliability_assessment_id:
          item.task_reliability_assessment_id ?? item.id ?? 0,
        task_reliability_assessment_answer:
          item.task_reliability_assessment_answer ?? '',
        condition: item.condition ?? '',
      }))
    : [];

// Helper: Map basic task fields from template to risk form
const mapBasicTaskFields = (payload: any) => ({
  task_requiring_ra: payload?.task_requiring_ra ?? '',
  task_duration: payload?.task_duration ?? '',
  task_alternative_consideration: payload?.task_alternative_consideration ?? '',
  task_rejection_reason: payload?.task_rejection_reason ?? '',
  worst_case_scenario: payload?.worst_case_scenario ?? '',
  recovery_measures: payload?.recovery_measures ?? '',
});

// Helper: Map assessor and location fields from template to risk form
const mapAssessorAndLocationFields = (payload: any) => ({
  assessor: payload?.assessor ?? null,
  vessel_ownership_id: payload?.vessel_ownership_id ?? null,
  office_id: payload?.office_id ?? null,
  office_name: payload?.office_name ?? '',
  date_risk_assessment: payload?.date_risk_assessment ?? null,
});

// Helper: Map metadata fields from template to risk form
const mapMetadataFields = (payload: any) => ({
  updated_at: payload?.updated_at ?? undefined,
  updated_by: payload?.updated_by ?? undefined,
  created_by: payload?.created_by ?? undefined,
  ra_level: payload?.ra_level ?? undefined,
  approval_date: payload?.approval_date ?? undefined,
  risk_approver: payload?.risk_approver ?? [],
  template_id: payload?.id ?? undefined,
});

export const transformTemplateToRisk = (payload: any): RiskForm => {
  return {
    ...mapBasicTaskFields(payload),
    ...mapAssessorAndLocationFields(payload),
    status: TemplateFormStatus.DRAFT,
    approval_required: [],
    risk_team_member: [],
    risk_category: mapRiskCategoryFromTemplate(payload?.template_category),
    risk_hazard: mapRiskHazardFromTemplate(payload?.template_hazards),
    parameters: mapParametersFromTemplate(payload?.template_parameter),
    risk_job: mapRiskJobsFromTemplate(payload?.template_job),
    risk_task_reliability_assessment: mapReliabilityAssessmentFromTemplate(
      payload?.template_task_reliability_assessment,
    ),
    ...mapMetadataFields(payload),
  };
};

/**
 * Formats a Date object to YYYY-MM-DD string format without timezone issues.
 * Uses local date components to avoid timezone conversion problems.
 *
 * @param {Date} date - The Date object to format
 * @returns {string} Date string in YYYY-MM-DD format
 *
 * @description
 * Manually constructs date string using local date components:
 * - Extracts year, month, and day from the Date object
 * - Pads month and day with leading zeros if needed
 * - Avoids timezone conversion issues that can occur with toISOString()
 *
 * @example
 * const date = new Date(2023, 11, 25); // December 25, 2023
 * const formatted = formatDateToYYYYMMDD(date);
 * // Returns: '2023-12-25'
 */
export const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Calculates the final approval date for a risk assessment based on approvers and RA level.
 * Handles different approval workflows depending on the risk assessment level.
 *
 * @param {RAItemFull['risk_approver']} approvers - Array of risk approver objects
 * @param {RaLevel} raLevel - The Risk Assessment level (ROUTINE, CRITICAL, SPECIAL, etc.)
 * @returns {string | undefined} The final approval date or undefined if not fully approved
 *
 * @description
 * Approval logic varies by RA level:
 *
 * **ROUTINE Level:**
 * - Finds approvers without approval_order (direct approvers)
 * - Returns approval_date of the first approved user (status === 1)
 *
 * **CRITICAL/SPECIAL Levels:**
 * - Requires all reviewers with approval_order to be approved
 * - Checks that all have status === 1 and approval_status in [1, 3]
 * - Returns approval_date of the last approver in the sequence
 * - Returns undefined if not all reviewers have approved
 *
 * **Other Levels:**
 * - Returns undefined (no specific approval logic)
 *
 * @example
 * const approvers = [
 *   { approval_order: 1, status: 1, approval_status: 1, approval_date: '2023-12-01' },
 *   { approval_order: 2, status: 1, approval_status: 1, approval_date: '2023-12-02' }
 * ];
 * const finalDate = getFinalApprovalDate(approvers, RaLevel.CRITICAL);
 * // Returns: '2023-12-02' (last approver's date)
 */
export const getFinalApprovalDate = (
  approvers: RAItemFull['risk_approver'],
  raLevel: RaLevel,
) => {
  if (raLevel === RaLevel.ROUTINE) {
    return (
      approvers
        .filter(user => !user.approval_order)
        .find(user => user.status === 1)?.approval_date || undefined
    );
  }

  if ([RaLevel.CRITICAL, RaLevel.SPECIAL].includes(raLevel)) {
    const allReviewersApproved = approvers
      .filter(user => user.approval_order)
      .every(
        user => user.status === 1 && [1, 3].includes(user.approval_status ?? 0),
      );

    if (allReviewersApproved) {
      // Use slice().sort() to avoid mutating the original array
      const sortedApprovers = approvers
        .slice()
        .sort((a, b) => (a.approval_order ?? 0) - (b.approval_order ?? 0));
      return (
        sortedApprovers[sortedApprovers.length - 1]?.approval_date || undefined
      );
    }

    return undefined;
  }
};

/**
 * Interface for a single vessel/office option in dropdown components.
 * Represents an individual selectable item with vessel-specific metadata.
 */
export interface SingleVesselOfficeOption {
  value: number | string;
  label: string;
  vesselId?: number;
}

/**
 * Interface for grouped vessel/office options in dropdown components.
 * Represents a category of options (e.g., 'Active Vessels', 'Offices') with nested items.
 */
export interface GroupedVesselOfficeOption {
  label: string;
  data: Array<{
    id: number | string;
    name: string;
    vesselId?: number;
  }>;
}

/**
 * Creates grouped vessel/office options for dropdown components.
 * Organizes vessels by status and includes offices as a separate group.
 *
 * @param {any[]} vesselListForRisk - Array of vessel ownership data
 * @param {any[]} officeListForRisk - Array of office data
 * @param {Record<string, string>} vesselStatusAndLabelName - Mapping of vessel status codes to display labels
 * @returns {GroupedVesselOfficeOption[]} Array of grouped options for dropdown components
 *
 * @description
 * Processes vessel and office data into a structured format for grouped dropdowns:
 *
 * **Vessel Processing:**
 * - Groups vessels by their status (active, pending_handover, handed_over)
 * - Sorts groups alphabetically by status
 * - Uses vesselStatusAndLabelName to get display labels for each status
 * - Preserves vessel metadata (vesselId) for vessel-specific operations
 *
 * **Office Processing:**
 * - Creates a separate 'Offices' group
 * - Maps office data to consistent structure
 *
 * **Output Structure:**
 * - Each group has a label and data array
 * - Data items have id, name, and optional vesselId
 * - Maintains consistent interface for both vessels and offices
 *
 * @example
 * const vessels = [
 *   { id: 1, name: 'Ship A', status: 'active', vessel: { id: 101 } },
 *   { id: 2, name: 'Ship B', status: 'active', vessel: { id: 102 } }
 * ];
 * const offices = [{ id: 1, value: 'Singapore Office' }];
 * const statusLabels = { active: 'Active Vessels' };
 *
 * const grouped = createGroupedVesselOfficeOptions(vessels, offices, statusLabels);
 * // Returns: [
 * //   { label: 'Active Vessels', data: [{ id: 1, name: 'Ship A', vesselId: 101 }, ...] },
 * //   { label: 'Offices', data: [{ id: 1, name: 'Singapore Office' }] }
 * // ]
 */
export const createGroupedVesselOfficeOptions = (
  vesselListForRisk: any[],
  officeListForRisk: any[],
  vesselStatusAndLabelName: Record<string, string>,
): GroupedVesselOfficeOption[] => {
  const vesselOpts = vesselListForRisk.map(item => ({
    value: item.id,
    label: item.name,
    vesselId: item.vessel.id,
    status: item.status,
  }));

  const officeOpts = officeListForRisk.map(item => ({
    value: item.id,
    label: item.value,
  }));

  // Create grouped options for the dropdown
  const vesselsByStatus = _.groupBy(vesselOpts, 'status');
  const groupedOptions: GroupedVesselOfficeOption[] = [
    ...Object.entries(vesselsByStatus)
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([status, vessels]) => ({
        label: vesselStatusAndLabelName[String(status)] || status,
        data: vessels.map(vessel => ({
          id: vessel.value,
          name: vessel.label,
          vesselId: vessel.vesselId,
        })),
      })),
    {
      label: 'Offices',
      data: officeOpts.map(office => ({
        id: office.value,
        name: office.label,
      })),
    },
  ];

  return groupedOptions;
};

/**
 * Finds a selected vessel/office option from grouped options by its value.
 * Searches through all groups to locate the option with the matching ID.
 *
 * @param {GroupedVesselOfficeOption[]} groupedOptions - Array of grouped options to search through
 * @param {number | string | null} currentValue - The value/ID to search for
 * @returns {SingleVesselOfficeOption | null} The matching option or null if not found
 *
 * @description
 * Searches through the grouped options structure to find a specific item:
 * - Iterates through all groups (vessel status groups and offices)
 * - Searches within each group's data array for matching ID
 * - Returns the first match found with proper structure
 * - Preserves vesselId metadata if present
 * - Returns null if no match found or currentValue is null/undefined
 *
 * This is useful for:
 * - Setting initial selected values in dropdowns
 * - Validating selected options
 * - Converting between ID and full option objects
 *
 * @example
 * const grouped = [
 *   { label: 'Active Vessels', data: [{ id: 1, name: 'Ship A', vesselId: 101 }] },
 *   { label: 'Offices', data: [{ id: 2, name: 'Office B' }] }
 * ];
 *
 * const option = findSelectedVesselOfficeOption(grouped, 1);
 * // Returns: { value: 1, label: 'Ship A', vesselId: 101 }
 *
 * const notFound = findSelectedVesselOfficeOption(grouped, 999);
 * // Returns: null
 */
export const findSelectedVesselOfficeOption = (
  groupedOptions: GroupedVesselOfficeOption[],
  currentValue: number | string | null,
): SingleVesselOfficeOption | null => {
  if (!currentValue) return null;

  for (const group of groupedOptions) {
    const found = group.data.find(item => item.id === currentValue);
    if (found) {
      return {
        value: found.id,
        label: found.name,
        vesselId: (found as any).vesselId,
      };
    }
  }
  return null;
};
